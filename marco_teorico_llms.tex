\subsection{Arquitecturas Transformer y Large Language Models}
Los Large Language Models modernos se basan fundamentalmente en la arquitectura Transformer \cite{vaswani2017attention}, que revolucionó el campo del procesamiento de lenguaje natural al reemplazar las redes neuronales recurrentes y convolucionales con un mecanismo de atención puro, permitiendo un procesamiento más eficiente y paralelo de secuencias de texto. Los avances recientes han demostrado capacidades emergentes extraordinarias \cite{wei2024emergent} que trascienden las aplicaciones tradicionales de NLP.

La arquitectura Transformer se compone de dos componentes principales: un encoder y un decoder, cada uno formado por múltiples capas idénticas. Cada capa del encoder contiene dos sub-capas principales: un mecanismo de multi-head self-attention y una red neuronal feed-forward completamente conectada. El mecanismo de atención permite al modelo enfocarse en diferentes partes de la secuencia de entrada al procesar cada elemento, capturando dependencias a largo plazo de manera más efectiva que las arquitecturas anteriores.

El mecanismo de self-attention se puede expresar matemáticamente como:
\begin{equation}
\text{Attention}(Q, K, V) = \text{softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)V
\end{equation}

donde $Q$, $K$, y $V$ representan las matrices de queries, keys, y values respectivamente, y $d_k$ es la dimensión de los keys.

Los LLMs más prominentes utilizan variaciones de esta arquitectura base, con desarrollos recientes que incluyen arquitecturas Mixture-of-Experts \cite{huang2024mixture, lo2024closer} y modelos multimodales unificados \cite{huang2025unifork}:

\textbf{Modelos basados en Encoder (BERT-like):} Estos modelos utilizan únicamente la parte del encoder de la arquitectura Transformer. Son especialmente efectivos para tareas de comprensión del lenguaje como clasificación de texto, análisis de sentimientos, y respuesta a preguntas.

\textbf{Modelos basados en Decoder (GPT-like):} Modelos como GPT-4 \cite{openai2024gpt4}, LLaMA 2 \cite{touvron2024llama2}, y Claude 3 \cite{anthropic2024claude3} utilizan únicamente la parte del decoder, optimizados para la generación de texto. Estos modelos han demostrado capacidades emergentes extraordinarias en razonamiento y generación.

\textbf{Modelos Multimodales:} Los modelos más avanzados como Gemini \cite{team2024gemini} integran capacidades de procesamiento de texto, imagen y otros tipos de datos, representando la frontera actual de la investigación en LLMs.

\textbf{Modelos Encoder-Decoder:} Modelos como T5 y BART utilizan la arquitectura completa Transformer, siendo especialmente efectivos para tareas de traducción, resumen, y otras tareas de transformación texto-a-texto.

\subsection{Técnicas de entrenamiento y optimización}
El entrenamiento de Large Language Models es un proceso complejo que ha evolucionado significativamente, incorporando técnicas avanzadas de optimización y alineación \cite{li2025fine}.

\textbf{Pre-entrenamiento:} Esta etapa inicial involucra entrenar el modelo en vastas cantidades de texto no etiquetado utilizando objetivos de aprendizaje auto-supervisado. Los enfoques modernos consideran leyes de escalamiento optimizadas \cite{kaplan2024scaling} para maximizar la eficiencia del entrenamiento.

\textbf{Instruction Tuning:} Las técnicas modernas de instruction tuning \cite{zhang2024instruction} han evolucionado hacia enfoques más sofisticados que mejoran la capacidad de seguir instrucciones complejas y generalizar across dominios.

\textbf{Parameter-Efficient Fine-Tuning (PEFT):} Técnicas como LoRA avanzado \cite{hu2024lora} y otros métodos PEFT permiten adaptar modelos grandes de manera eficiente, reduciendo significativamente los requisitos computacionales.

\textbf{Reinforcement Learning from Human Feedback (RLHF):} Las técnicas de RLHF han avanzado considerablemente \cite{chen2024rlhf, liu2025safe}, incluyendo métodos para garantizar seguridad en modelos multimodales y técnicas de aprendizaje de recompensas más sofisticadas \cite{zhang2025sailing}.

\textbf{Constitutional AI:} Los enfoques de alineación basados en principios constitucionales \cite{anthropic2024constitutional} representan una evolución importante en la creación de sistemas de IA más seguros y alineados con valores humanos.

\subsection{Evaluación y métricas de rendimiento}
La evaluación de Large Language Models ha evolucionado hacia enfoques más comprehensivos \cite{chang2024survey} que consideran múltiples dimensiones de rendimiento, incluyendo capacidades emergentes, alineación, y seguridad.

\textbf{Evaluación de Capacidades Emergentes:} Los marcos modernos evalúan capacidades como razonamiento chain-of-thought, in-context learning \cite{dong2024survey}, y habilidades de prompt engineering avanzado \cite{liu2024systematic}.

\textbf{Evaluación Multimodal:} Para modelos multimodales, se han desarrollado benchmarks especializados que evalúan la integración de diferentes modalidades y la reducción de alucinaciones \cite{zhang2024hallucination}.

\textbf{Evaluación de Alineación y Seguridad:} Los métodos modernos incluyen evaluación de alineación con valores humanos, detección de sesgos, y evaluación de robustez en aplicaciones críticas.

\textbf{Benchmarks Especializados:} Además de los benchmarks tradicionales, se han desarrollado evaluaciones específicas para modelos especializados \cite{wang2025specialized} y aplicaciones de dominio como diagnóstico médico \cite{chen2025disease}.

\subsection{Desafíos y limitaciones actuales}
Los Large Language Models modernos enfrentan desafíos específicos que han evolucionado con el avance de la tecnología \cite{kaddour2024challenges}:

\textbf{Alucinaciones Multimodales:} Los modelos multimodales presentan desafíos únicos en términos de alucinaciones \cite{zhang2024hallucination}, requiriendo técnicas especializadas para mejorar la factualidad across modalidades.

\textbf{Escalamiento Eficiente:} A pesar de los avances en leyes de escalamiento \cite{kaplan2024scaling}, el desarrollo de modelos más grandes sigue requiriendo recursos computacionales masivos, motivando investigación en arquitecturas Mixture-of-Experts \cite{huang2024mixture}.

\textbf{Seguridad y Alineación:} Los desafíos de seguridad han evolucionado hacia problemas más complejos que requieren técnicas avanzadas como Safe RLHF \cite{liu2025safe} y Constitutional AI \cite{anthropic2024constitutional}.

\textbf{Interpretabilidad Avanzada:} La comprensión de mecanismos internos en modelos de gran escala sigue siendo un desafío fundamental, especialmente en modelos multimodales y arquitecturas complejas.

\textbf{Foundation Models y Generalización:} Los desafíos relacionados con foundation models \cite{bommasani2024opportunities} incluyen la creación de modelos base más versátiles y la especialización inteligente para dominios específicos.

\textbf{Sostenibilidad y Democratización:} El desarrollo de técnicas para hacer los LLMs más accesibles y ambientalmente sostenibles representa una prioridad creciente en la investigación actual.
