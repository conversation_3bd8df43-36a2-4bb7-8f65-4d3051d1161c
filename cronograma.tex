\chapter[CRONOGRAMA]{CRONOGRAMA}

El desarrollo de esta investigación sobre Large Language Models se estructura en un cronograma de 24 meses, dividido en fases secuenciales y paralelas que permiten un progreso sistemático hacia los objetivos planteados. A continuación se detalla la planificación temporal, incluyendo los principales hitos y entregables.

\section{Cronograma general}

\subsection{Año 1 - Fundamentos y análisis teórico}

\textbf{Meses 1-3: Revisión sistemática de literatura}
\begin{itemize}
\item Búsqueda y recopilación de fuentes bibliográficas
\item Análisis y categorización de publicaciones relevantes
\item Identificación de gaps en el conocimiento actual
\item Elaboración del estado del arte comprehensivo
\end{itemize}
\textbf{Entregable:} Documento de estado del arte (50-60 páginas)

\textbf{Meses 4-6: Análisis teórico y arquitectural}
\begin{itemize}
\item Estudio profundo de arquitecturas Transformer
\item Análisis matemático de mecanismos de atención
\item Implementación de componentes fundamentales
\item Comparación teórica de diferentes variantes
\end{itemize}
\textbf{Entregable:} Marco teórico detallado y implementaciones base

\textbf{Meses 7-9: Configuración experimental}
\begin{itemize}
\item Establecimiento de infraestructura computacional
\item Configuración de frameworks y herramientas
\item Desarrollo de pipelines de experimentación
\item Pruebas preliminares de configuración
\end{itemize}
\textbf{Entregable:} Entorno experimental funcional y documentado

\textbf{Meses 10-12: Experimentos iniciales}
\begin{itemize}
\item Fine-tuning de modelos pre-entrenados
\item Evaluación en benchmarks estándar
\item Análisis de técnicas PEFT básicas
\item Documentación de resultados preliminares
\end{itemize}
\textbf{Entregable:} Reporte de experimentos iniciales y primer artículo de conferencia

\subsection{Año 2 - Experimentación avanzada y contribuciones}

\textbf{Meses 13-15: Experimentación avanzada}
\begin{itemize}
\item Implementación de técnicas de optimización avanzadas
\item Experimentos con diferentes escalas de modelos
\item Análisis de scaling laws y comportamiento emergente
\item Desarrollo de métricas de evaluación especializadas
\end{itemize}
\textbf{Entregable:} Conjunto de experimentos comprehensivos y análisis de scaling

\textbf{Meses 16-18: Desarrollo de aplicaciones}
\begin{itemize}
\item Implementación de prototipos de aplicación
\item Evaluación en casos de uso reales
\item Análisis de rendimiento y eficiencia
\item Optimización para deployment práctico
\end{itemize}
\textbf{Entregable:} Prototipos funcionales y evaluación de aplicaciones

\textbf{Meses 19-21: Contribuciones metodológicas}
\begin{itemize}
\item Desarrollo de técnicas o mejoras innovadoras
\item Validación experimental de contribuciones
\item Comparación con métodos estado del arte
\item Análisis de impacto y limitaciones
\end{itemize}
\textbf{Entregable:} Contribuciones originales validadas y segundo artículo de journal

\textbf{Meses 22-24: Síntesis y documentación final}
\begin{itemize}
\item Análisis comprehensivo de todos los resultados
\item Redacción de la tesis doctoral
\item Preparación de presentaciones y defensa
\item Publicación de recursos y código para la comunidad
\end{itemize}
\textbf{Entregable:} Tesis doctoral completa y recursos públicos

\section{Hitos principales}

\begin{table}[H]
\centering
\begin{tabular}{|l|l|l|}
\hline
\textbf{Mes} & \textbf{Hito} & \textbf{Descripción} \\
\hline
3 & Estado del arte & Revisión sistemática completa \\
\hline
6 & Marco teórico & Fundamentos teóricos establecidos \\
\hline
9 & Infraestructura & Entorno experimental operativo \\
\hline
12 & Primera publicación & Artículo en conferencia internacional \\
\hline
15 & Experimentos avanzados & Análisis de scaling completado \\
\hline
18 & Prototipos & Aplicaciones funcionales desarrolladas \\
\hline
21 & Segunda publicación & Artículo en journal de alto impacto \\
\hline
24 & Tesis completa & Documento final y defensa \\
\hline
\end{tabular}
\caption{Cronograma de hitos principales}
\end{table}

\section{Recursos necesarios}

\subsection{Recursos computacionales}
\begin{itemize}
\item Acceso a cluster de GPUs de alta gama (A100 o V100)
\item Almacenamiento de alta velocidad para datasets grandes
\item Ancho de banda suficiente para descarga de modelos y datos
\item Servicios de cloud computing para experimentos escalables
\end{itemize}

\subsection{Recursos de software}
\begin{itemize}
\item Licencias de frameworks de deep learning (PyTorch, TensorFlow)
\item Herramientas de experimentación (Weights \& Biases, MLflow)
\item Acceso a APIs de modelos comerciales para comparación
\item Software de análisis estadístico y visualización
\end{itemize}

\subsection{Recursos humanos}
\begin{itemize}
\item Supervisión académica especializada en NLP/ML
\item Colaboración con expertos en áreas específicas
\item Acceso a evaluadores humanos para validación
\item Soporte técnico para infraestructura computacional
\end{itemize}

\section{Gestión de riesgos}

\subsection{Riesgos técnicos}
\begin{itemize}
\item \textbf{Limitaciones computacionales:} Mitigación mediante uso eficiente de recursos y técnicas de optimización
\item \textbf{Problemas de reproducibilidad:} Control estricto de versiones y documentación detallada
\item \textbf{Cambios rápidos en el campo:} Flexibilidad en el plan para incorporar desarrollos relevantes
\end{itemize}

\subsection{Riesgos de cronograma}
\begin{itemize}
\item \textbf{Retrasos en experimentos:} Buffer de tiempo incorporado y priorización de objetivos
\item \textbf{Complejidad subestimada:} Revisión periódica del plan y ajustes según progreso
\item \textbf{Dependencias externas:} Identificación temprana y planes de contingencia
\end{itemize}

\section{Evaluación y seguimiento}

\subsection{Métricas de progreso}
\begin{itemize}
\item Número de experimentos completados por mes
\item Calidad y cantidad de resultados obtenidos
\item Progreso en escritura de publicaciones
\item Feedback de supervisores y colaboradores
\end{itemize}

\subsection{Revisiones periódicas}
\begin{itemize}
\item Reuniones semanales con supervisor principal
\item Revisiones mensuales de progreso general
\item Evaluaciones trimestrales con comité de tesis
\item Presentaciones semestrales a la comunidad académica
\end{itemize}
